<?xml version="1.0" encoding="UTF-8"?>

<?import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>

<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.HBox?>
<AnchorPane fx:id="mainPanel" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308"
            stylesheets="@../Styles/Login.css" xmlns="http://javafx.com/javafx/23.0.1"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.store.app.petstore.Controllers.LoginController">
    <HBox>

        <AnchorPane styleClass="sidebar-left" AnchorPane.bottomAnchor="0.0"
                    AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0" HBox.hgrow="ALWAYS">
            <VBox alignment="CENTER" HBox.hgrow="ALWAYS"
                  spacing="10.0" AnchorPane.bottomAnchor="40.0"
                  AnchorPane.leftAnchor="40.0" AnchorPane.rightAnchor="40.0"
                  AnchorPane.topAnchor="40.0">
                <ImageView fitHeight="100.0" fitWidth="100.0" pickOnBounds="true"
                           preserveRatio="true">
                    <image>
                        <Image url="@../Images/logo.png"/>
                    </image>
                </ImageView>
                <Label styleClass="titleLogin" text="ĐĂNG NHẬP"/>
                <Label styleClass="lblHello"
                       text="Chào mừng bạn quay trở lại! Hãy đăng nhập vào tài khoản của bạn!"
                       textAlignment="CENTER" wrapText="true"/>
                <Label styleClass="lbl" text="Tên tài khoản"/>
                <TextField fx:id="usernameField" prefHeight="26.0" promptText="Nhập tên tài khoản"
                           styleClass="txtField" maxWidth="1.7976931348623157E308"/>
                <Label styleClass="lbl" text="Mật khẩu"/>
                <AnchorPane maxWidth="1.7976931348623157E308" HBox.hgrow="ALWAYS" >
                    <PasswordField fx:id="passwordField" prefHeight="26.0"
                                   promptText="Nhập mật khẩu" styleClass="txtField"
                                   AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0"/>
                    <TextField fx:id="showPassword" prefHeight="26.0" promptText="Nhập mật khẩu"
                               styleClass="txtField" visible="false" AnchorPane.leftAnchor="0.0"
                               AnchorPane.rightAnchor="0.0"/>
                    <FontAwesomeIconView fx:id="eyeIcon" glyphName="EYE_SLASH" layoutX="265.0"
                                         layoutY="18.0" styleClass="eyeIcon" text="" AnchorPane.rightAnchor="5.0">
                        <cursor>
                            <Cursor fx:constant="HAND"/>
                        </cursor>
                    </FontAwesomeIconView>
                </AnchorPane>
                <Label fx:id="errorLabel" styleClass="error-label" text="" visible="false"/>
                <Hyperlink fx:id="forgotPasswordLink" styleClass="linkForgotPassword"
                           text="Quên mật khẩu?"/>
                <Button fx:id="loginButton" mnemonicParsing="false" styleClass="btnLogin"
                        text="Đăng nhập" maxWidth="1.7976931348623157E308"/>
            </VBox>
        </AnchorPane>

        <ImageView fx:id="sidebarImage"
                   HBox.hgrow="ALWAYS"
                   pickOnBounds="true"
                   preserveRatio="true"
                   AnchorPane.topAnchor="0.0"
                   AnchorPane.bottomAnchor="0.0"
                   AnchorPane.rightAnchor="0.0">
            <Image url="@../Images/sidebar.png"/>
        </ImageView>
    </HBox>
</AnchorPane>
